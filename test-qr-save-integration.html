<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WCDP QR Save Integration Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #005a87;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        #console-output {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .input-group {
            margin: 10px 0;
        }
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .input-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>WCDP QR Save Integration Test</h1>
    
    <div class="test-container">
        <h2>Integration Status</h2>
        <p>This page tests the QR code save integration functionality for WCDP.</p>
        <div class="status info">
            <strong>Note:</strong> This test page should be loaded within the WCDP designer environment to work properly.
        </div>
        
        <button class="test-button" onclick="runDiagnostics()">Run Diagnostics</button>
        <button class="test-button" onclick="clearConsole()">Clear Console</button>
    </div>
    
    <div class="test-container">
        <h2>Test QR Text Input</h2>
        <div class="input-group">
            <label for="test-qr-text">QR Code Text:</label>
            <input type="text" id="test-qr-text" value="https://example.com/test-qr-code" placeholder="Enter text for QR code">
        </div>
        
        <button class="test-button" onclick="testQRGeneration()">Test QR Generation</button>
        <button class="test-button" onclick="testSaveIntegration()">Test Save Integration</button>
        <button class="test-button" onclick="testSaveButton()">Test Save Button Click</button>
    </div>
    
    <div class="test-container">
        <h2>Canvas Information</h2>
        <button class="test-button" onclick="checkCanvasStatus()">Check Canvas Status</button>
        <button class="test-button" onclick="testCanvasMerge()">Test Canvas Merge</button>
    </div>
    
    <div class="test-container">
        <h2>Console Output</h2>
        <div id="console-output"></div>
    </div>

    <script>
        // Console output capture
        const consoleOutput = document.getElementById('console-output');
        const originalLog = console.log;
        const originalError = console.error;
        
        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '[ERROR]' : '[LOG]';
            consoleOutput.textContent += `${timestamp} ${prefix} ${message}\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        function clearConsole() {
            consoleOutput.textContent = '';
        }
        
        function runDiagnostics() {
            console.log('=== Running WCDP QR Save Integration Diagnostics ===');
            
            if (typeof window.diagnoseSaveQRIntegration === 'function') {
                window.diagnoseSaveQRIntegration();
            } else {
                console.error('diagnoseSaveQRIntegration function not available');
                console.log('This suggests the QR code generator plugin is not loaded or active');
            }
        }
        
        function testQRGeneration() {
            const qrText = document.getElementById('test-qr-text').value;
            console.log('Testing QR generation with text:', qrText);
            
            if (typeof window.testQRWithForceInit === 'function') {
                window.testQRWithForceInit(qrText);
            } else {
                console.error('testQRWithForceInit function not available');
            }
        }
        
        function testSaveIntegration() {
            const qrText = document.getElementById('test-qr-text').value;
            console.log('Testing save integration with QR text:', qrText);
            
            if (typeof window.testSaveWithQRIntegration === 'function') {
                window.testSaveWithQRIntegration(qrText);
            } else {
                console.error('testSaveWithQRIntegration function not available');
            }
        }
        
        function testSaveButton() {
            const qrText = document.getElementById('test-qr-text').value;
            console.log('Testing save button click with QR text:', qrText);
            
            if (typeof window.testSaveButtonWithQR === 'function') {
                window.testSaveButtonWithQR(qrText);
            } else {
                console.error('testSaveButtonWithQR function not available');
            }
        }
        
        function checkCanvasStatus() {
            console.log('=== Canvas Status Check ===');
            
            const canvasEl = document.getElementById('wcdp-canvas-editor');
            console.log('Canvas element exists:', !!canvasEl);
            
            if (canvasEl) {
                console.log('Canvas dimensions:', canvasEl.width + 'x' + canvasEl.height);
                console.log('Has Fabric.js:', !!canvasEl.__fabric);
                
                if (canvasEl.__fabric) {
                    console.log('Fabric objects count:', canvasEl.__fabric.getObjects().length);
                }
            }
            
            console.log('Global wcdp_canvas_editor:', typeof window.wcdp_canvas_editor);
        }
        
        function testCanvasMerge() {
            console.log('Testing canvas merge functionality...');
            
            // This would test the merge functionality if canvas is available
            const canvasEl = document.getElementById('wcdp-canvas-editor');
            if (canvasEl) {
                try {
                    const dataURL = canvasEl.toDataURL('image/png');
                    console.log('Successfully got canvas data URL, length:', dataURL.length);
                } catch (e) {
                    console.error('Failed to get canvas data URL:', e.message);
                }
            } else {
                console.error('Canvas element not found');
            }
        }
        
        // Initialize
        console.log('WCDP QR Save Integration Test Page Loaded');
        console.log('Use the buttons above to test different aspects of the integration');
    </script>
</body>
</html>
