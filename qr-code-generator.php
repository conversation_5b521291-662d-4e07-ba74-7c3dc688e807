<?php
/*
Plugin Name: QR Code Generator Shortcode
Description: Generates a QR code with logo using QR Code Generator API.
Version: 1.0
Author: ChatGPT
*/

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Add debug function to check if plugin is loading
function wcdp_qr_debug_plugin_loaded() {
    error_log('WCDP QR Plugin: Plugin file loaded successfully');

    // Add a simple JavaScript alert to confirm plugin is active
    if (isset($_GET['dp_mode']) && in_array($_GET['dp_mode'], ['designer', 'save'])) {
        add_action('wp_footer', function() {
            echo '<script>console.log("WCDP QR Plugin: Plugin is active and loaded on WCDP page");</script>';
        });
    }
}
add_action('init', 'wcdp_qr_debug_plugin_loaded');

function generate_custom_qr_code($text, $options = array()) {
    $api_url = 'https://api.qr-code-generator.com/v1/create?access-token=QgC3EUE21Rtt70g-VDqiUp5vAMcTsgcbJ_7K251iHXUt9G9lyZ7-o0fbKyekEAoP';

    // Default options
    $defaults = array(
        'frame_name'     => 'no-frame',
        'image_format'   => 'SVG',
        'qr_code_logo'   => 'scan-me-square',
        'foreground_color' => '#000000',
        'background_color' => '#FFFFFF',
        'error_correction' => 'M', // L, M, Q, H
        'size' => 200
    );

    $options = wp_parse_args($options, $defaults);

    $body = array(
        'frame_name'     => $options['frame_name'],
        'qr_code_text'   => $text,
        'image_format'   => $options['image_format'],
        'qr_code_logo'   => $options['qr_code_logo'],
        'foreground_color' => $options['foreground_color'],
        'background_color' => $options['background_color'],
        'error_correction' => $options['error_correction'],
        'size' => $options['size']
    );

    $response = wp_remote_post($api_url, array(
        'headers' => array(
            'Content-Type' => 'application/json'
        ),
        'body' => wp_json_encode($body),
        'timeout' => 30
    ));

    if (is_wp_error($response)) {
        return array(
            'success' => false,
            'error' => $response->get_error_message()
        );
    }

    $response_code = wp_remote_retrieve_response_code($response);
    if ($response_code !== 200) {
        return array(
            'success' => false,
            'error' => 'API returned status code: ' . $response_code
        );
    }

    $svg = wp_remote_retrieve_body($response);

    // For shortcode usage, return wrapped HTML
    if (!isset($options['ajax_request'])) {
        return '<div class="custom-qr-wrapper">' . $svg . '</div>';
    }

    // For AJAX usage, return raw SVG
    return array(
        'success' => true,
        'svg' => $svg
    );
}

function qr_code_shortcode($atts) {
    $a = shortcode_atts(array(
        'text' => 'https://bakedbot.ai/'
    ), $atts);

    return generate_custom_qr_code($a['text']);
}
add_shortcode('generate_qr', 'qr_code_shortcode');

// Debug shortcode for testing
function qr_debug_shortcode($atts) {
    $a = shortcode_atts(array(
        'text' => 'Debug Test QR Code'
    ), $atts);

    $result = generate_custom_qr_code($a['text'], array('ajax_request' => true));

    $output = '<div style="border: 1px solid #ccc; padding: 20px; margin: 20px 0;">';
    $output .= '<h3>QR Code Debug Test</h3>';
    $output .= '<p><strong>Text:</strong> ' . esc_html($a['text']) . '</p>';

    if (is_array($result)) {
        if ($result['success']) {
            $output .= '<p style="color: green;"><strong>Status:</strong> Success</p>';
            $output .= '<div style="border: 1px solid #ddd; padding: 10px; max-width: 300px;">';
            $output .= $result['svg'];
            $output .= '</div>';
        } else {
            $output .= '<p style="color: red;"><strong>Status:</strong> Error</p>';
            $output .= '<p><strong>Error:</strong> ' . esc_html($result['error']) . '</p>';
        }
    } else {
        $output .= '<p style="color: orange;"><strong>Status:</strong> Unexpected response</p>';
        $output .= '<pre>' . esc_html(print_r($result, true)) . '</pre>';
    }

    $output .= '</div>';
    return $output;
}
add_shortcode('qr_debug', 'qr_debug_shortcode');

// AJAX handler for WCDP integration
function wcdp_custom_qr_code_ajax() {
    // Add debugging
    error_log('WCDP QR Code AJAX called with data: ' . print_r($_POST, true));

    // Verify nonce for security
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'wcdp_qr_nonce')) {
        error_log('WCDP QR Code: Nonce verification failed');
        wp_send_json_error('Security check failed');
        return;
    }

    // Validate required fields
    if (!isset($_POST['text']) || empty(trim($_POST['text']))) {
        error_log('WCDP QR Code: No text provided');
        wp_send_json_error('No text provided for QR code generation');
        return;
    }

    $text = sanitize_text_field($_POST['text']);
    $foreground_color = isset($_POST['foreground_color']) ? sanitize_hex_color($_POST['foreground_color']) : '#000000';
    $background_color = isset($_POST['background_color']) ? sanitize_hex_color($_POST['background_color']) : '#FFFFFF';
    $error_correction = isset($_POST['error_correction']) ? sanitize_text_field($_POST['error_correction']) : 'MEDIUM';
    $logo = isset($_POST['logo']) ? sanitize_text_field($_POST['logo']) : 'scan-me-square';
    $size = isset($_POST['size']) ? intval($_POST['size']) : 200;

    // Map WCDP error correction levels to API format
    $error_correction_map = array(
        'LOW' => 'L',
        'MEDIUM' => 'M',
        'QUARTILE' => 'Q',
        'HIGH' => 'H'
    );

    $api_error_correction = isset($error_correction_map[$error_correction])
        ? $error_correction_map[$error_correction]
        : 'M';

    $options = array(
        'foreground_color' => $foreground_color ?: '#000000',
        'background_color' => $background_color ?: '#FFFFFF',
        'error_correction' => $api_error_correction,
        'qr_code_logo' => $logo ?: 'scan-me-square',
        'size' => $size ?: 200,
        'ajax_request' => true
    );

    error_log('WCDP QR Code: Calling generate_custom_qr_code with options: ' . print_r($options, true));

    $result = generate_custom_qr_code($text, $options);

    error_log('WCDP QR Code: API result: ' . print_r($result, true));

    if (is_array($result)) {
        wp_send_json($result);
    } else {
        wp_send_json_error('Unexpected response format from QR code generator');
    }
}

// Register AJAX handlers for both logged in and non-logged in users
add_action('wp_ajax_wcdp_custom_qr_code', 'wcdp_custom_qr_code_ajax');
add_action('wp_ajax_nopriv_wcdp_custom_qr_code', 'wcdp_custom_qr_code_ajax');

// Enqueue script to provide nonce for AJAX calls
function wcdp_qr_enqueue_scripts() {
    // Check if we're on a page that might have WCDP editor
    $should_enqueue = false;

    if (is_admin()) {
        $should_enqueue = true;
    } elseif (isset($_GET['dp_mode']) && in_array($_GET['dp_mode'], ['designer', 'save'])) {
        $should_enqueue = true;
    } elseif (function_exists('wcdp_check_mode_design_page') && wcdp_check_mode_design_page()) {
        $should_enqueue = true;
    }

    // Debug logging
    error_log('WCDP QR: Enqueue check - should_enqueue: ' . ($should_enqueue ? 'true' : 'false'));
    error_log('WCDP QR: Current URL: ' . (isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : 'N/A'));

    if ($should_enqueue) {
        // Enqueue to footer and make sure it's available
        wp_add_inline_script('jquery', '
            window.wcdp_qr_ajax = {
                ajax_url: "' . admin_url('admin-ajax.php') . '",
                nonce: "' . wp_create_nonce('wcdp_qr_nonce') . '"
            };
            console.log("WCDP QR AJAX variables loaded:", window.wcdp_qr_ajax);

            // Test function to verify script loading
            window.testQRScriptLoaded = function() {
                console.log("QR Code script is loaded and working!");
                return true;
            };

            // Create custom QR code function and bind to button
            jQuery(document).ready(function($) {
                console.log("QR Code plugin: Document ready, setting up custom QR function");

                // Add diagnostic functions immediately
                window.diagnoseWCDP = function() {
                    console.log("=== WCDP Diagnosis ===");

                    // Check overlay loader
                    var overlayLoader = document.querySelector(".wcdp_overlay_loader");
                    console.log("Overlay loader:", {
                        exists: !!overlayLoader,
                        display: overlayLoader ? overlayLoader.style.display : "N/A",
                        offsetParent: overlayLoader ? !!overlayLoader.offsetParent : "N/A",
                        visible: overlayLoader ? overlayLoader.offsetParent !== null : "N/A"
                    });

                    // Check canvas element
                    var canvasEl = document.getElementById("wcdp-canvas-editor");
                    console.log("Canvas element:", {
                        exists: !!canvasEl,
                        hasFabric: canvasEl ? !!canvasEl.__fabric : false,
                        fabricType: canvasEl ? typeof canvasEl.__fabric : "N/A"
                    });

                    // Check global variables
                    console.log("Global variables:", {
                        wcdp_canvas_editor: typeof window.wcdp_canvas_editor,
                        fabric: typeof window.fabric,
                        jQuery: typeof window.jQuery,
                        wcdp_parameters: typeof window.wcdp_parameters
                    });

                    // Check if WCDP scripts are loaded
                    var scripts = document.querySelectorAll("script[src*=\"wcdp\"]");
                    console.log("WCDP scripts loaded:", scripts.length);

                    // Check for loading indicators
                    var dpLoader = document.querySelector(".dp-loader-editor");
                    console.log("DP Loader:", {
                        exists: !!dpLoader,
                        display: dpLoader ? dpLoader.style.display : "N/A",
                        visible: dpLoader ? dpLoader.offsetParent !== null : "N/A"
                    });

                    return {
                        overlayHidden: !overlayLoader || overlayLoader.style.display === "none" || !overlayLoader.offsetParent,
                        canvasReady: canvasEl && canvasEl.__fabric && canvasEl.__fabric.add,
                        globalsReady: typeof window.wcdp_canvas_editor !== "undefined" && window.wcdp_canvas_editor
                    };
                };

                console.log("Diagnostic functions loaded. Try: diagnoseWCDP()");

                // Create our custom QR function
                window.wcdp_custom_qr_generate = function(ins) {
                    console.log("Custom QR function called with ins:", ins);

                    // For new QR codes, we don\'t need to check existing objects
                    var isUpdate = false;
                    if (ins === "change" && typeof wcdp_get_selection_obj === "function") {
                        var obj = wcdp_get_selection_obj();
                        isUpdate = obj.length == 1 && obj[0].clas == "qr";
                    }

                    // Get QR code parameters with better debugging
                    var textField = $("#wcdp-text-box-qr"),
                        fg = $("#wcdp-fg-qr-color"),
                        bg = $("#wcdp-bg-qr-color"),
                        levelField = $("#wcdp-qr-level"),
                        logoField = $("#wcdp-qr-logo");

                    // Get values with fallbacks
                    var qrText = textField.val() || textField.text() || "";
                    var level = levelField.val() || levelField.find("option:selected").val() || "MEDIUM";
                    var logo = logoField.length ? (logoField.val() || "scan-me-square") : "scan-me-square";

                    // If text is still empty, try to get it from the input directly
                    if (!qrText && textField.length) {
                        qrText = textField[0].value || "";
                    }

                    var params = {"text": qrText, "level": level, "logo": logo};

                    console.log("Custom QR parameters:", params);
                    console.log("QR Text field details:", {
                        value: textField.val(),
                        text: textField.text(),
                        directValue: textField.length ? textField[0].value : "no field",
                        placeholder: textField.attr("placeholder")
                    });
                    console.log("Available elements:", {
                        textField: textField.length,
                        fgColor: fg.length,
                        bgColor: bg.length,
                        levelSelect: levelField.length,
                        logoSelect: logoField.length
                    });

                    // Debug: try to set and read the text field
                    if (textField.length && !qrText) {
                        console.log("Attempting to set test text...");
                        textField.val("Debug Test QR");
                        textField.trigger("change");
                        qrText = textField.val();
                        console.log("After setting test text:", qrText);
                        params.text = qrText;
                    }

                    // Validate required text
                    if (!qrText || qrText.trim() === "") {
                        console.log("No QR text provided");
                        if (typeof wcdp_jbox_msg_modal === "function") {
                            wcdp_jbox_msg_modal("Please enter text for QR code generation");
                        } else {
                            alert("Please enter text for QR code generation");
                        }
                        return false;
                    }

                            // Show loading if available
                            if (typeof wcdp_loading !== "undefined" && wcdp_loading.show) {
                                wcdp_loading.show();
                            }

                            // Make AJAX call to custom QR code plugin
                            jQuery.ajax({
                                url: window.wcdp_qr_ajax.ajax_url,
                                type: "POST",
                                data: {
                                    action: "wcdp_custom_qr_code",
                                    nonce: window.wcdp_qr_ajax.nonce,
                                    text: qrText,
                                    foreground_color: fg.length ? fg.spectrum("get").toHexString() : "#000000",
                                    background_color: bg.length ? bg.spectrum("get").toHexString() : "#FFFFFF",
                                    error_correction: level,
                                    logo: logo,
                                    size: 200
                                },
                                timeout: 30000,
                                success: function(response) {
                                    console.log("Custom QR AJAX success:", response);

                                    if (typeof wcdp_loading !== "undefined" && wcdp_loading.hide) {
                                        wcdp_loading.hide();
                                    }

                                    if (response.success && response.svg) {
                                        // Load the SVG from the API response
                                        if (typeof fabric !== "undefined" && fabric.loadSVGFromString) {
                                            fabric.loadSVGFromString(response.svg, function(objects, options){
                                                console.log("SVG loaded, objects:", objects.length);

                                                var newQR = fabric.util.groupSVGElements(objects, options),
                                                    size = typeof wcdp_parameters !== "undefined" && wcdp_parameters.qr_size ? parseInt(wcdp_parameters.qr_size) : 80;

                                                console.log("QR object created:", newQR);
                                                // Function to find canvas editor with retries
                                                function findCanvasEditor() {
                                                    var canvasEditor = null;
                                                    var possibleNames = ["wcdp_canvas_editor", "canvas", "fabricCanvas", "editor", "foundCanvas"];

                                                    // Try global variables first
                                                    for (var i = 0; i < possibleNames.length; i++) {
                                                        if (typeof window[possibleNames[i]] !== "undefined" && window[possibleNames[i]] && window[possibleNames[i]].add) {
                                                            canvasEditor = window[possibleNames[i]];
                                                            console.log("Found canvas editor as:", possibleNames[i]);
                                                            break;
                                                        }
                                                    }

                                                    // Try to find canvas by element ID
                                                    if (!canvasEditor) {
                                                        var canvasElement = document.getElementById("wcdp-canvas-editor");
                                                        if (canvasElement && typeof fabric !== "undefined") {
                                                            // Try to get fabric canvas from the element
                                                            if (canvasElement.__fabric) {
                                                                canvasEditor = canvasElement.__fabric;
                                                                console.log("Found canvas editor via element.__fabric");
                                                            }
                                                        }
                                                    }

                                                    // Deep search all canvas elements
                                                    if (!canvasEditor) {
                                                        var allCanvasElements = document.querySelectorAll("canvas");
                                                        for (var i = 0; i < allCanvasElements.length; i++) {
                                                            var canvas = allCanvasElements[i];
                                                            if (canvas.__fabric && canvas.__fabric.add) {
                                                                canvasEditor = canvas.__fabric;
                                                                console.log("Found canvas editor via deep search:", canvas.id || "canvas-" + i);
                                                                break;
                                                            }
                                                        }
                                                    }

                                                    return canvasEditor;
                                                }

                                                // Try to find canvas with retries
                                                function addQRToCanvasWithRetries(qrObject, maxRetries) {
                                                    maxRetries = maxRetries || 5;
                                                    var retryCount = 0;

                                                    function attemptAdd() {
                                                        var canvasEditor = findCanvasEditor();

                                                        console.log("Attempt", retryCount + 1, "- Canvas editor found:", !!canvasEditor);

                                                        if (canvasEditor && canvasEditor.add) {
                                                            try {
                                                                if (canvasEditor.discardActiveGroup) {
                                                                    canvasEditor.discardActiveGroup();
                                                                }
                                                                canvasEditor.add(qrObject);
                                                                if (canvasEditor.setActiveObject) {
                                                                    canvasEditor.setActiveObject(qrObject);
                                                                }
                                                                if (canvasEditor.renderAll) {
                                                                    canvasEditor.renderAll();
                                                                }
                                                                console.log("QR code successfully added to canvas!");
                                                                return true;
                                                            } catch (e) {
                                                                console.error("Error adding QR to canvas:", e);
                                                            }
                                                        }

                                                        retryCount++;
                                                        if (retryCount < maxRetries) {
                                                            console.log("Canvas not ready, retrying in", retryCount * 500, "ms...");
                                                            setTimeout(attemptAdd, retryCount * 500);
                                                        } else {
                                                            console.error("Failed to add QR code to canvas after", maxRetries, "attempts");
                                                            console.log("Available window objects:", Object.keys(window).filter(k => k.includes("canvas") || k.includes("editor") || k.includes("wcdp")));
                                                        }
                                                        return false;
                                                    }

                                                    return attemptAdd();
                                                }

                                                // Set CMYK values for color management if available
                                                if (newQR.paths && newQR.paths.length >= 2) {
                                                    if (bg.length && bg.attr("cmyk")) newQR.paths[0].fillCMYK = bg.attr("cmyk");
                                                    if (fg.length && fg.attr("cmyk")) newQR.paths[1].fillCMYK = fg.attr("cmyk");
                                                }

                                                if(isUpdate && typeof wcdp_get_selection_obj === "function"){
                                                    // Update existing QR code
                                                    var obj = wcdp_get_selection_obj();
                                                    if (obj.length > 0 && newQR.paths && obj[0].paths) {
                                                        obj[0].paths[0] = newQR.paths[0];
                                                        obj[0].paths[1] = newQR.paths[1];
                                                        obj[0].set({
                                                            width: newQR.width,
                                                            height: newQR.height,
                                                            dataDP: params
                                                        }).setCoords();
                                                        console.log("Updated existing QR code");

                                                        // Render the canvas
                                                        var canvasEditor = findCanvasEditor();
                                                        if (canvasEditor && canvasEditor.renderAll) {
                                                            canvasEditor.renderAll();
                                                        }
                                                    }
                                                } else{
                                                    // Add new QR code
                                                    newQR.set({
                                                        top: 50,
                                                        left: 50,
                                                        clas: "qr",
                                                        strokeWidth: 0,
                                                        dataDP: params
                                                    }).scaleToWidth(size).scaleToHeight(size * newQR.height / newQR.width);

                                                    console.log("Adding new QR code to canvas with retry mechanism");

                                                    // Use retry mechanism to add QR code
                                                    addQRToCanvasWithRetries(newQR, 10);
                                                }

                                                if (typeof wcdp_check_obj_center === "function") {
                                                    wcdp_check_obj_center(newQR);
                                                }

                                                if (typeof wcdp_save_canvas_state === "function") {
                                                    wcdp_save_canvas_state();
                                                }

                                                console.log("QR code successfully added to canvas");
                                            });
                                        } else {
                                            console.error("Fabric.js not available");
                                        }
                                    } else {
                                        // Handle API error
                                        var errorMsg = response.error || "Failed to generate QR code. Please try again.";
                                        if (typeof wcdp_jbox_msg_modal === "function") {
                                            wcdp_jbox_msg_modal("QR Code Error: " + errorMsg);
                                        } else {
                                            alert("QR Code Error: " + errorMsg);
                                        }
                                    }
                                },
                                error: function(xhr, status, error) {
                                    console.error("Custom QR AJAX Error:", {xhr: xhr, status: status, error: error});

                                    if (typeof wcdp_loading !== "undefined" && wcdp_loading.hide) {
                                        wcdp_loading.hide();
                                    }

                                    var errorMessage = "Network error occurred while generating QR code.";
                                    if (status === "timeout") {
                                        errorMessage = "QR code generation timed out. Please try again.";
                                    } else if (xhr.status === 403) {
                                        errorMessage = "Permission denied. Please refresh the page and try again.";
                                    } else if (xhr.status === 500) {
                                        errorMessage = "Server error occurred. Please try again later.";
                                    }

                                    if (typeof wcdp_jbox_msg_modal === "function") {
                                        wcdp_jbox_msg_modal(errorMessage + " If the problem persists, please contact support.");
                                    } else {
                                        alert(errorMessage);
                                    }
                                }
                            });
                };

                console.log("Custom QR function created");

                // Function to wait for canvas initialization
                function waitForCanvasInit(callback, maxWait) {
                    maxWait = maxWait || 30000; // 30 seconds max
                    var startTime = Date.now();

                    function checkCanvas() {
                        var canvasEl = document.getElementById("wcdp-canvas-editor");
                        if (canvasEl && canvasEl.__fabric && canvasEl.__fabric.add) {
                            console.log("Canvas is now initialized!");
                            window.wcdp_canvas_editor = canvasEl.__fabric;
                            callback(canvasEl.__fabric);
                            return;
                        }

                        // Also check upper canvas
                        var allCanvases = document.querySelectorAll("canvas");
                        for (var i = 0; i < allCanvases.length; i++) {
                            if (allCanvases[i].__fabric && allCanvases[i].__fabric.add) {
                                console.log("Found initialized canvas:", allCanvases[i].className || "canvas-" + i);
                                window.wcdp_canvas_editor = allCanvases[i].__fabric;
                                callback(allCanvases[i].__fabric);
                                return;
                            }
                        }

                        if (Date.now() - startTime < maxWait) {
                            setTimeout(checkCanvas, 200);
                        } else {
                            console.log("Canvas initialization timeout after", maxWait, "ms");
                            callback(null);
                        }
                    }

                    checkCanvas();
                }

                // The WCDP system already has wcdp_make_qrcode_canvas function integrated with our API
                // No need to override - just ensure our AJAX endpoint is available
                console.log("QR Code integration ready - WCDP will use our custom API automatically");

                // Add global test function
                window.testQRCodeGeneration = function(testText) {
                    testText = testText || "Test QR Code";
                    console.log("Testing QR code generation with text:", testText);

                    jQuery.ajax({
                        url: window.wcdp_qr_ajax.ajax_url,
                        type: "POST",
                        data: {
                            action: "wcdp_custom_qr_code",
                            nonce: window.wcdp_qr_ajax.nonce,
                            text: testText,
                            foreground_color: "#000000",
                            background_color: "#FFFFFF",
                            error_correction: "MEDIUM",
                            logo: "scan-me-square",
                            size: 200
                        },
                        success: function(response) {
                            console.log("QR Code test success:", response);

                            // If successful, try to add to canvas
                            if (response.success && response.svg && typeof fabric !== "undefined") {
                                fabric.loadSVGFromString(response.svg, function(objects, options){
                                    console.log("Test: SVG loaded, objects:", objects.length);

                                    var newQR = fabric.util.groupSVGElements(objects, options);
                                    newQR.set({
                                        top: 100,
                                        left: 100,
                                        clas: "qr",
                                        strokeWidth: 0,
                                        dataDP: {text: testText, level: "MEDIUM", logo: "scan-me-square"}
                                    }).scaleToWidth(80);

                                    if (typeof wcdp_canvas_editor !== "undefined" && wcdp_canvas_editor) {
                                        wcdp_canvas_editor.add(newQR).setActiveObject(newQR);
                                        wcdp_canvas_editor.renderAll();
                                        console.log("Test QR code added to canvas successfully!");
                                    } else {
                                        console.log("Canvas editor not available for test");
                                    }
                                });
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error("QR Code test error:", {xhr: xhr, status: status, error: error});
                        }
                    });
                };

                // Add function to test with form values
                window.testQRWithForm = function() {
                    console.log("Testing QR with current form values...");

                    // Set a test value first
                    $("#wcdp-text-box-qr").val("Form Test QR Code");

                    // Wait a moment then call our function
                    setTimeout(function() {
                        window.wcdp_custom_qr_generate();
                    }, 100);
                };

                // Add function to test with canvas waiting
                window.testQRWithCanvasWait = function() {
                    console.log("Testing QR with canvas initialization wait...");

                    // Set a test value first
                    $("#wcdp-text-box-qr").val("Canvas Wait Test QR Code");

                    // Wait for canvas to be ready
                    waitForCanvasInit(function(canvas) {
                        if (canvas) {
                            console.log("Canvas is ready, generating QR code");
                            window.wcdp_custom_qr_generate();
                        } else {
                            console.log("Canvas wait timeout, trying anyway");
                            window.wcdp_custom_qr_generate();
                        }
                    });
                };

                // Add function to try triggering canvas initialization
                window.triggerCanvasInit = function() {
                    console.log("Attempting to trigger canvas initialization...");

                    // Try clicking on canvas area to trigger initialization
                    var canvasEl = document.getElementById("wcdp-canvas-editor");
                    if (canvasEl) {
                        console.log("Clicking on canvas to trigger initialization");
                        canvasEl.click();
                        canvasEl.focus();

                        // Try triggering mouse events
                        var mouseEvent = new MouseEvent("mousedown", {
                            view: window,
                            bubbles: true,
                            cancelable: true,
                            clientX: 100,
                            clientY: 100
                        });
                        canvasEl.dispatchEvent(mouseEvent);

                        setTimeout(function() {
                            var mouseUpEvent = new MouseEvent("mouseup", {
                                view: window,
                                bubbles: true,
                                cancelable: true,
                                clientX: 100,
                                clientY: 100
                            });
                            canvasEl.dispatchEvent(mouseUpEvent);
                        }, 100);
                    }

                    // Try to find and call WCDP initialization functions
                    var initFunctions = ["wcdp_init_canvas", "wcdp_canvas_init", "initCanvas", "init_canvas"];
                    initFunctions.forEach(function(funcName) {
                        if (typeof window[funcName] === "function") {
                            console.log("Calling", funcName);
                            try {
                                window[funcName]();
                            } catch (e) {
                                console.log("Error calling", funcName, ":", e);
                            }
                        }
                    });

                    // Check if canvas is now initialized
                    setTimeout(function() {
                        findCanvasEditor();
                    }, 1000);
                };

                // Add function to force create a Fabric canvas while preserving content
                window.forceCreateCanvas = function(preserveContent) {
                    console.log("Force creating Fabric canvas...");

                    var canvasEl = document.getElementById("wcdp-canvas-editor");
                    if (canvasEl && typeof fabric !== "undefined") {
                        try {
                            var existingContent = null;

                            // Try to preserve existing content if requested
                            if (preserveContent !== false) {
                                // Check if there is already a canvas with content
                                if (canvasEl.__fabric && canvasEl.__fabric.getObjects) {
                                    existingContent = canvasEl.__fabric.toJSON();
                                    console.log("Preserving existing canvas content with", canvasEl.__fabric.getObjects().length, "objects");
                                } else {
                                    // Try to get canvas data as image
                                    try {
                                        var canvasData = canvasEl.toDataURL();
                                        if (canvasData && canvasData !== "data:,") {
                                            existingContent = {type: "image", data: canvasData};
                                            console.log("Preserving canvas as image data");
                                        }
                                    } catch (e) {
                                        console.log("Could not preserve canvas as image:", e);
                                    }
                                }
                            }

                            // Create a new Fabric canvas
                            var fabricCanvas = new fabric.Canvas("wcdp-canvas-editor");
                            window.wcdp_canvas_editor = fabricCanvas;
                            console.log("Fabric canvas created successfully!");

                            // Restore existing content if we have it
                            if (existingContent) {
                                if (existingContent.type === "image") {
                                    // Restore as background image
                                    fabric.Image.fromURL(existingContent.data, function(img) {
                                        fabricCanvas.setBackgroundImage(img, fabricCanvas.renderAll.bind(fabricCanvas));
                                        console.log("Restored canvas background image");
                                    });
                                } else {
                                    // Restore as JSON objects
                                    fabricCanvas.loadFromJSON(existingContent, function() {
                                        fabricCanvas.renderAll();
                                        console.log("Restored canvas objects from JSON");
                                    });
                                }
                            }

                            // Test adding a QR code
                            $("#wcdp-text-box-qr").val("Force Canvas Test");
                            setTimeout(function() {
                                window.wcdp_custom_qr_generate();
                            }, 500);

                        } catch (e) {
                            console.error("Error creating Fabric canvas:", e);
                        }
                    } else {
                        console.log("Canvas element or Fabric.js not available");
                    }
                };

                // Add function to find existing WCDP canvas more aggressively
                window.findExistingWCDPCanvas = function() {
                    console.log("Searching for existing WCDP canvas...");

                    // Check all possible global variables
                    var possibleCanvasVars = [
                        "wcdp_canvas_editor", "wcdp_canvas", "canvas_editor", "canvas",
                        "fabricCanvas", "editor", "wcdp_fabric_canvas", "wcdp_editor"
                    ];

                    for (var i = 0; i < possibleCanvasVars.length; i++) {
                        var varName = possibleCanvasVars[i];
                        if (typeof window[varName] !== "undefined" && window[varName]) {
                            var obj = window[varName];
                            console.log("Checking", varName, ":", typeof obj, obj);

                            if (obj && typeof obj === "object") {
                                // Check if it has Fabric canvas methods
                                if (obj.add && obj.renderAll && obj.getObjects) {
                                    console.log("*** FOUND EXISTING WCDP CANVAS ***", varName);
                                    console.log("Objects on canvas:", obj.getObjects().length);
                                    window.wcdp_canvas_editor = obj;
                                    return obj;
                                }

                                // Check if it contains a canvas property
                                if (obj.canvas && obj.canvas.add && obj.canvas.renderAll) {
                                    console.log("*** FOUND CANVAS IN OBJECT ***", varName + ".canvas");
                                    console.log("Objects on canvas:", obj.canvas.getObjects().length);
                                    window.wcdp_canvas_editor = obj.canvas;
                                    return obj.canvas;
                                }
                            }
                        }
                    }

                    console.log("No existing WCDP canvas found");
                    return null;
                };

                // Test function to check if WCDP canvas is available
                window.checkWCDPCanvas = function() {
                    var canvasEl = document.getElementById("wcdp-canvas-editor");
                    console.log("Canvas element found:", !!canvasEl);

                    if (canvasEl) {
                        console.log("Canvas element details:", {
                            id: canvasEl.id,
                            width: canvasEl.width,
                            height: canvasEl.height,
                            hasFabric: !!canvasEl.__fabric,
                            fabricType: typeof canvasEl.__fabric
                        });

                        if (canvasEl.__fabric) {
                            console.log("WCDP canvas is available!");
                            console.log("Canvas objects:", canvasEl.__fabric.getObjects().length);
                            console.log("Canvas size:", canvasEl.__fabric.width, "x", canvasEl.__fabric.height);
                            return true;
                        }
                    }

                    console.log("WCDP canvas not yet available");
                    return false;
                };

                // Function to diagnose WCDP initialization status
                window.diagnoseWCDP = function() {
                    console.log("=== WCDP Diagnosis ===");

                    // Check overlay loader
                    var overlayLoader = document.querySelector(".wcdp_overlay_loader");
                    console.log("Overlay loader:", {
                        exists: !!overlayLoader,
                        display: overlayLoader ? overlayLoader.style.display : "N/A",
                        offsetParent: overlayLoader ? !!overlayLoader.offsetParent : "N/A",
                        visible: overlayLoader ? overlayLoader.offsetParent !== null : "N/A"
                    });

                    // Check canvas element
                    var canvasEl = document.getElementById("wcdp-canvas-editor");
                    console.log("Canvas element:", {
                        exists: !!canvasEl,
                        hasFabric: canvasEl ? !!canvasEl.__fabric : false,
                        fabricType: canvasEl ? typeof canvasEl.__fabric : "N/A"
                    });

                    // Check global variables
                    console.log("Global variables:", {
                        wcdp_canvas_editor: typeof window.wcdp_canvas_editor,
                        fabric: typeof window.fabric,
                        jQuery: typeof window.jQuery,
                        wcdp_parameters: typeof window.wcdp_parameters
                    });

                    // Check if WCDP scripts are loaded
                    var scripts = document.querySelectorAll("script[src*=\"wcdp\"]");
                    console.log("WCDP scripts loaded:", scripts.length);

                    // Check for loading indicators
                    var dpLoader = document.querySelector(".dp-loader-editor");
                    console.log("DP Loader:", {
                        exists: !!dpLoader,
                        display: dpLoader ? dpLoader.style.display : "N/A",
                        visible: dpLoader ? dpLoader.offsetParent !== null : "N/A"
                    });

                    return {
                        overlayHidden: !overlayLoader || overlayLoader.style.display === "none" || !overlayLoader.offsetParent,
                        canvasReady: canvasEl && canvasEl.__fabric && canvasEl.__fabric.add,
                        globalsReady: typeof window.wcdp_canvas_editor !== "undefined" && window.wcdp_canvas_editor
                    };
                };

                // Function to force WCDP initialization if it is stuck
                window.forceWCDPInit = function() {
                    console.log("Attempting to force WCDP initialization...");

                    // Hide overlay loader manually if it is stuck
                    var overlayLoader = document.querySelector(".wcdp_overlay_loader");
                    if (overlayLoader) {
                        overlayLoader.style.display = "none";
                        console.log("Manually hid overlay loader");
                    }

                    // Try to create canvas if it does not exist
                    var canvasEl = document.getElementById("wcdp-canvas-editor");
                    if (canvasEl && !canvasEl.__fabric && typeof fabric !== "undefined") {
                        console.log("Creating Fabric canvas manually...");
                        try {
                            window.wcdp_canvas_editor = new fabric.Canvas("wcdp-canvas-editor", {controlsAboveOverlay: true});
                            console.log("Fabric canvas created successfully");
                        } catch (e) {
                            console.log("Error creating Fabric canvas:", e);
                        }
                    }

                    // Check status after forcing
                    setTimeout(function() {
                        diagnoseWCDP();
                    }, 1000);
                };

                // Simple QR test function that works with current state
                window.testQRNow = function(qrText) {
                    qrText = qrText || "Test QR Now";
                    console.log("Testing QR generation with current state...");

                    // Check current status
                    var status = diagnoseWCDP();

                    // Try to set QR text
                    var textField = document.getElementById("wcdp-text-box-qr");
                    if (textField) {
                        textField.value = qrText;
                        console.log("Set QR text to:", qrText);

                        // Trigger change event
                        var event = new Event("change", { bubbles: true });
                        textField.dispatchEvent(event);
                    } else {
                        console.log("QR text field not found");
                        return false;
                    }

                    // If canvas is ready, try the QR button
                    if (status.canvasReady) {
                        console.log("Canvas is ready, clicking QR button...");
                        var qrButton = document.getElementById("wcdp-btn-make-qr");
                        if (qrButton) {
                            qrButton.click();
                            return true;
                        } else {
                            console.log("QR button not found");
                        }
                    } else {
                        console.log("Canvas not ready, trying force initialization...");
                        forceWCDPInit();

                        // Try again after force init
                        setTimeout(function() {
                            var qrButton = document.getElementById("wcdp-btn-make-qr");
                            if (qrButton) {
                                console.log("Trying QR button after force init...");
                                qrButton.click();
                            }
                        }, 2000);
                    }

                    return false;
                };

                // Add function to discover canvas objects
                window.findCanvasEditor = function() {
                    console.log("=== Canvas Editor Discovery ===");

                    // Check common variable names
                    var candidates = ["wcdp_canvas_editor", "canvas", "fabricCanvas", "editor", "canvasEditor"];
                    candidates.forEach(function(name) {
                        if (typeof window[name] !== "undefined") {
                            console.log(name + ":", typeof window[name], window[name]);
                            if (window[name] && typeof window[name] === "object") {
                                console.log("  - has add method:", typeof window[name].add === "function");
                                console.log("  - has renderAll method:", typeof window[name].renderAll === "function");
                            }
                        }
                    });

                    // Check canvas element
                    var canvasEl = document.getElementById("wcdp-canvas-editor");
                    console.log("Canvas element found:", !!canvasEl);
                    if (canvasEl) {
                        console.log("Canvas element __fabric:", !!canvasEl.__fabric);
                        if (canvasEl.__fabric) {
                            console.log("Fabric canvas methods:", {
                                add: typeof canvasEl.__fabric.add,
                                renderAll: typeof canvasEl.__fabric.renderAll
                            });
                        }
                    }

                    // Check all window objects containing "canvas" or "wcdp"
                    var relevantObjects = Object.keys(window).filter(function(key) {
                        return key.toLowerCase().includes("canvas") ||
                               key.toLowerCase().includes("wcdp") ||
                               key.toLowerCase().includes("editor");
                    });
                    console.log("Relevant window objects:", relevantObjects);

                    // Deep search for Fabric canvas instances
                    console.log("=== Deep Canvas Search ===");
                    var allCanvasElements = document.querySelectorAll("canvas");
                    console.log("Found", allCanvasElements.length, "canvas elements");

                    for (var i = 0; i < allCanvasElements.length; i++) {
                        var canvas = allCanvasElements[i];
                        console.log("Canvas", i + ":", {
                            id: canvas.id,
                            className: canvas.className,
                            hasFabric: !!canvas.__fabric,
                            fabricMethods: canvas.__fabric ? {
                                add: typeof canvas.__fabric.add,
                                renderAll: typeof canvas.__fabric.renderAll
                            } : null
                        });

                        if (canvas.__fabric && canvas.__fabric.add) {
                            console.log("*** FOUND WORKING FABRIC CANVAS ***", canvas.id || "no-id");
                            window.foundCanvas = canvas.__fabric;
                        }
                    }

                    return relevantObjects;
                };

                console.log("QR Code test function available: testQRCodeGeneration()");

                // Monitor for WCDP canvas initialization
                var canvasCheckInterval = setInterval(function() {
                    var canvasEl = document.getElementById("wcdp-canvas-editor");
                    if (canvasEl && canvasEl.__fabric && canvasEl.__fabric.add) {
                        console.log("WCDP canvas is now initialized!");
                        window.wcdp_canvas_editor = canvasEl.__fabric;
                        clearInterval(canvasCheckInterval);

                        // Log canvas info
                        console.log("Canvas objects:", canvasEl.__fabric.getObjects().length);
                        console.log("Canvas size:", canvasEl.__fabric.width, "x", canvasEl.__fabric.height);
                    }
                }, 1000);

                // Clear the interval after 60 seconds to avoid infinite checking
                setTimeout(function() {
                    clearInterval(canvasCheckInterval);
                }, 60000);
            });
        ');
    }
}
add_action('wp_enqueue_scripts', 'wcdp_qr_enqueue_scripts');
add_action('admin_enqueue_scripts', 'wcdp_qr_enqueue_scripts');

// Admin notice if WCDP is not active
function wcdp_qr_admin_notice() {
    if (!function_exists('wcdp_check_mode_design_page')) {
        echo '<div class="notice notice-warning is-dismissible">';
        echo '<p><strong>QR Code Generator:</strong> This plugin requires WooCommerce Designer Pro to be active for full functionality.</p>';
        echo '</div>';
    }
}
add_action('admin_notices', 'wcdp_qr_admin_notice');

// Check if API is accessible (for debugging)
function wcdp_qr_test_api_connection() {
    if (!current_user_can('manage_options')) {
        return;
    }

    $test_result = generate_custom_qr_code('Test QR Code', array('ajax_request' => true));

    if (isset($_GET['test_qr_api']) && $_GET['test_qr_api'] === '1') {
        if ($test_result['success']) {
            wp_die('QR Code API is working correctly!');
        } else {
            wp_die('QR Code API Error: ' . $test_result['error']);
        }
    }
}
add_action('admin_init', 'wcdp_qr_test_api_connection');

// Hook the enqueue function to WordPress with multiple hooks
add_action('wp_enqueue_scripts', 'wcdp_qr_enqueue_scripts');
add_action('admin_enqueue_scripts', 'wcdp_qr_enqueue_scripts');
add_action('wp_head', 'wcdp_qr_enqueue_scripts');
add_action('wp_footer', 'wcdp_qr_enqueue_scripts');

// Alternative approach - add script directly to footer on designer pages
function wcdp_qr_direct_script() {
    if (isset($_GET['dp_mode']) && in_array($_GET['dp_mode'], ['designer', 'save'])) {
        ?>
        <script type="text/javascript">
        console.log("WCDP QR: Direct script loading attempt");

        // Set up WCDP QR AJAX variables
        window.wcdp_qr_ajax = {
            ajax_url: "<?php echo admin_url('admin-ajax.php'); ?>",
            nonce: "<?php echo wp_create_nonce('wcdp_qr_nonce'); ?>"
        };
        console.log("WCDP QR AJAX variables set:", window.wcdp_qr_ajax);

        // Test function to verify script loading
        window.testQRScriptLoaded = function() {
            console.log("QR Code script is loaded and working!");
            console.log("WCDP QR AJAX available:", typeof window.wcdp_qr_ajax !== 'undefined');
            return true;
        };

        // Simple diagnostic function
        window.diagnoseWCDP = function() {
            console.log("=== WCDP Diagnosis ===");

            var canvasEl = document.getElementById("wcdp-canvas-editor");
            console.log("Canvas element:", {
                exists: !!canvasEl,
                hasFabric: canvasEl ? !!canvasEl.__fabric : false,
                fabricType: canvasEl ? typeof canvasEl.__fabric : "N/A"
            });

            console.log("Global variables:", {
                wcdp_canvas_editor: typeof window.wcdp_canvas_editor,
                fabric: typeof window.fabric,
                jQuery: typeof window.jQuery
            });

            return {
                canvasReady: canvasEl && canvasEl.__fabric && canvasEl.__fabric.add
            };
        };

        // Function to wait for WCDP canvas initialization
        window.waitForWCDPCanvas = function(callback, maxWait) {
            maxWait = maxWait || 30000; // 30 seconds
            var startTime = Date.now();
            var checkInterval = 1000; // Check every second

            console.log("Waiting for WCDP canvas to initialize...");

            function checkCanvas() {
                var canvasEl = document.getElementById("wcdp-canvas-editor");
                var isReady = canvasEl && canvasEl.__fabric && canvasEl.__fabric.add;
                var elapsed = Date.now() - startTime;

                console.log("Canvas check:", {
                    canvasExists: !!canvasEl,
                    hasFabric: canvasEl ? !!canvasEl.__fabric : false,
                    isReady: isReady,
                    elapsed: elapsed + "ms"
                });

                if (isReady) {
                    console.log("WCDP canvas is ready!");
                    window.wcdp_canvas_editor = canvasEl.__fabric;
                    if (callback) callback(canvasEl.__fabric);
                    return true;
                }

                if (elapsed < maxWait) {
                    setTimeout(checkCanvas, checkInterval);
                } else {
                    console.log("Timeout waiting for WCDP canvas");
                    if (callback) callback(null);
                }

                return false;
            }

            checkCanvas();
        };

        // Function to test QR generation once canvas is ready
        window.testQRWithCanvas = function(qrText) {
            qrText = qrText || "Test QR Code";

            waitForWCDPCanvas(function(canvas) {
                if (canvas) {
                    console.log("Canvas ready! Objects on canvas:", canvas.getObjects().length);

                    // Set QR text in the field
                    var textField = document.getElementById("wcdp-text-box-qr");
                    if (textField) {
                        textField.value = qrText;
                        console.log("Set QR text to:", qrText);

                        // Trigger the QR button
                        var qrButton = document.getElementById("wcdp-btn-make-qr");
                        if (qrButton) {
                            console.log("Clicking QR button...");
                            qrButton.click();
                        } else {
                            console.log("QR button not found");
                        }
                    } else {
                        console.log("QR text field not found");
                    }
                } else {
                    console.log("Canvas initialization timed out");
                }
            });
        };

        // Function to manually initialize WCDP canvas
        window.forceInitWCDPCanvas = function() {
            console.log("Attempting to force initialize WCDP canvas...");

            var canvasEl = document.getElementById("wcdp-canvas-editor");
            if (!canvasEl) {
                console.log("Canvas element not found");
                return false;
            }

            if (typeof fabric === 'undefined') {
                console.log("Fabric.js not loaded");
                return false;
            }

            try {
                // Create Fabric canvas manually
                console.log("Creating Fabric canvas...");
                var fabricCanvas = new fabric.Canvas('wcdp-canvas-editor', {
                    controlsAboveOverlay: true
                });

                // Set global reference
                window.wcdp_canvas_editor = fabricCanvas;

                console.log("Fabric canvas created successfully!");
                console.log("Canvas size:", fabricCanvas.width, "x", fabricCanvas.height);

                // Try to load any existing design data
                if (typeof wcdp_parameters !== 'undefined' && wcdp_parameters.jsonSides) {
                    console.log("Found existing design data, attempting to load...");
                    var sides = Object.keys(wcdp_parameters.jsonSides);
                    if (sides.length > 0) {
                        var firstSide = sides[0];
                        var jsonData = wcdp_parameters.jsonSides[firstSide];
                        if (jsonData && jsonData[0]) {
                            try {
                                fabricCanvas.loadFromJSON(JSON.parse(jsonData[0]), function() {
                                    fabricCanvas.renderAll();
                                    console.log("Loaded existing design with", fabricCanvas.getObjects().length, "objects");
                                });
                            } catch (e) {
                                console.log("Error loading existing design:", e);
                            }
                        }
                    }
                }

                return true;
            } catch (e) {
                console.log("Error creating Fabric canvas:", e);
                return false;
            }
        };

        // Enhanced test function that forces initialization if needed
        window.testQRWithForceInit = function(qrText) {
            qrText = qrText || "Force Init QR Test";

            console.log("Testing QR with force initialization...");

            // Check if canvas is already ready
            var canvasEl = document.getElementById("wcdp-canvas-editor");
            if (canvasEl && canvasEl.__fabric) {
                console.log("Canvas already ready, proceeding with QR generation...");
                window.wcdp_canvas_editor = canvasEl.__fabric;
                generateQRNow(qrText);
                return;
            }

            // Try to force initialize
            console.log("Canvas not ready, forcing initialization...");
            if (forceInitWCDPCanvas()) {
                // Wait a moment for initialization to complete
                setTimeout(function() {
                    generateQRNow(qrText);
                }, 1000);
            } else {
                console.log("Failed to force initialize canvas");
            }
        };

        // Helper function to generate QR once canvas is ready
        function generateQRNow(qrText) {
            console.log("Generating QR code:", qrText);

            // Set QR text
            var textField = document.getElementById("wcdp-text-box-qr");
            if (textField) {
                textField.value = qrText;
                console.log("Set QR text field");

                // Instead of clicking the button, call our API directly
                console.log("Calling custom QR API directly...");
                generateQRDirectly(qrText);
            } else {
                console.log("QR text field not found");
            }
        }

        // Function to call our QR API directly, bypassing WCDP button
        function generateQRDirectly(qrText) {
            if (!window.wcdp_canvas_editor) {
                console.log("Canvas not available");
                return;
            }

            console.log("Making direct AJAX call to custom QR API...");

            // Get QR parameters
            var fgColor = document.getElementById("wcdp-fg-qr-color");
            var bgColor = document.getElementById("wcdp-bg-qr-color");
            var levelField = document.getElementById("wcdp-qr-level");
            var logoField = document.getElementById("wcdp-qr-logo");

            var params = {
                action: 'wcdp_custom_qr_code',
                nonce: window.wcdp_qr_ajax.nonce,
                text: qrText,
                foreground_color: fgColor ? (fgColor.value || '#000000') : '#000000',
                background_color: bgColor ? (bgColor.value || '#FFFFFF') : '#FFFFFF',
                error_correction: levelField ? levelField.value : 'MEDIUM',
                logo: logoField ? logoField.value : 'scan-me-square',
                size: 200
            };

            console.log("QR API parameters:", params);

            jQuery.ajax({
                url: window.wcdp_qr_ajax.ajax_url,
                type: 'POST',
                data: params,
                timeout: 30000,
                success: function(response) {
                    console.log("QR API success:", response);

                    if (response.success && response.svg) {
                        // Load SVG and add to canvas
                        fabric.loadSVGFromString(response.svg, function(objects, options) {
                            console.log("SVG loaded, objects:", objects.length);

                            if (objects.length > 0) {
                                var qrGroup = fabric.util.groupSVGElements(objects, options);
                                var canvas = window.wcdp_canvas_editor;

                                // Calculate proper center position within visible canvas area
                                // Position more towards the actual center for better visibility
                                var centerX = canvas.width * 0.5; // True center horizontally
                                var centerY = canvas.height * 0.5; // True center vertically

                                // Scale to appropriate size first
                                var targetSize = 120; // Slightly smaller for better fit
                                qrGroup.scaleToWidth(targetSize);

                                // Set QR properties with better positioning
                                qrGroup.set({
                                    left: centerX,
                                    top: centerY,
                                    clas: 'qr',
                                    strokeWidth: 0,
                                    selectable: true,
                                    evented: true,
                                    hasControls: true,
                                    hasBorders: true,
                                    lockMovementX: false,
                                    lockMovementY: false,
                                    lockScalingFlip: true, // Prevent flipping
                                    cornerStyle: 'circle',
                                    cornerSize: 8,
                                    transparentCorners: false,
                                    dataDP: {
                                        text: qrText,
                                        level: params.error_correction,
                                        logo: params.logo
                                    }
                                });

                                // Add to canvas first
                                canvas.add(qrGroup);

                                // Bring QR code to front (above all other objects)
                                canvas.bringToFront(qrGroup);

                                // Use setTimeout to ensure proper rendering before selection
                                setTimeout(function() {
                                    // Ensure QR is still on top after any canvas operations
                                    canvas.bringToFront(qrGroup);
                                    canvas.setActiveObject(qrGroup);
                                    canvas.renderAll();

                                    console.log("QR code successfully added to canvas!");
                                    console.log("Canvas objects count:", canvas.getObjects().length);
                                    console.log("QR code z-index:", canvas.getObjects().indexOf(qrGroup));
                                    console.log("QR position:", {
                                        left: qrGroup.left,
                                        top: qrGroup.top,
                                        width: Math.round(qrGroup.width * qrGroup.scaleX),
                                        height: Math.round(qrGroup.height * qrGroup.scaleY),
                                        scaleX: qrGroup.scaleX,
                                        scaleY: qrGroup.scaleY
                                    });
                                }, 100);

                                // Add event handlers to prevent disappearing and maintain z-index
                                qrGroup.on('selected', function() {
                                    console.log("QR code selected - keeping visible and on top");
                                    this.set('opacity', 1); // Ensure full opacity
                                    canvas.bringToFront(this); // Bring to front when selected
                                    canvas.renderAll();
                                });

                                qrGroup.on('deselected', function() {
                                    console.log("QR code deselected - maintaining visibility and top position");
                                    this.set('opacity', 1); // Keep visible when deselected
                                    canvas.bringToFront(this); // Keep on top even when deselected
                                    canvas.renderAll();
                                });

                                qrGroup.on('moving', function() {
                                    console.log("QR code being moved");
                                    // Ensure it stays on top while moving
                                    canvas.bringToFront(this);
                                });

                                qrGroup.on('modified', function() {
                                    console.log("QR code modified - ensuring top position");
                                    canvas.bringToFront(this);
                                    canvas.renderAll();
                                });

                                // Add canvas event to handle clicks that might affect layering
                                canvas.on('mouse:down', function(e) {
                                    // If clicking on the QR code, ensure it stays on top
                                    if (e.target === qrGroup) {
                                        console.log("Clicked on QR code - bringing to front");
                                        canvas.bringToFront(qrGroup);
                                    }
                                });

                            } else {
                                console.log("No objects in SVG");
                            }
                        });
                    } else {
                        console.log("QR API error:", response.error || 'Unknown error');
                    }
                },
                error: function(xhr, status, error) {
                    console.log("AJAX error:", status, error);
                }
            });
        }

        // Enhanced save design functionality with QR code integration
        window.wcdp_save_design_with_qr = function(designName, originalSaveFunction) {
            console.log("Save design with QR integration called");

            // Get QR text from the text field
            var qrTextField = document.getElementById("wcdp-text-box-qr");
            var qrText = qrTextField ? qrTextField.value.trim() : '';

            if (!qrText) {
                console.log("No QR text provided, proceeding with normal save");
                originalSaveFunction();
                return;
            }

            console.log("QR text found:", qrText, "- generating QR code and merging with canvas");

            // Check if we should use direct canvas approach
            var canvasEl = document.getElementById("wcdp-canvas-editor");
            var useFabric = canvasEl && canvasEl.__fabric && typeof fabric !== 'undefined';

            console.log("Canvas approach:", useFabric ? "Fabric.js" : "Direct canvas");

            // Generate QR code and merge with canvas
            if (useFabric) {
                generateQRAndMergeWithCanvas(qrText, function(success) {
                    if (success) {
                        console.log("QR code merged successfully, proceeding with save");
                    } else {
                        console.log("QR code merge failed, proceeding with normal save");
                    }
                    originalSaveFunction();
                });
            } else {
                generateQRAndMergeDirectly(qrText, function(success) {
                    if (success) {
                        console.log("QR code merged directly, proceeding with save");
                    } else {
                        console.log("Direct QR merge failed, proceeding with normal save");
                    }
                    originalSaveFunction();
                });
            }
        };

        // Function to generate QR code and merge directly (without Fabric.js)
        function generateQRAndMergeDirectly(qrText, callback) {
            console.log("Generating QR code directly for text:", qrText);

            jQuery.ajax({
                url: window.wcdp_qr_ajax.ajax_url,
                type: "POST",
                data: {
                    action: "wcdp_custom_qr_code",
                    nonce: window.wcdp_qr_ajax.nonce,
                    text: qrText,
                    foreground_color: "#000000",
                    background_color: "#FFFFFF",
                    error_correction: "MEDIUM",
                    logo: "scan-me-square",
                    size: 200
                },
                timeout: 15000,
                success: function(response) {
                    console.log("Direct QR API response:", response);

                    if (response.success && response.svg) {
                        // Store QR data for persistence
                        if (window.wcdp_qr_persistence) {
                            window.wcdp_qr_persistence.store(qrText, response.svg);
                        }
                        mergeQRDirectly(response.svg, callback);
                    } else {
                        console.log("Direct QR API error:", response.error || 'Unknown error');
                        callback(false);
                    }
                },
                error: function(xhr, status, error) {
                    console.error("Direct QR API AJAX error:", status, error);
                    callback(false);
                }
            });
        }

        // Function to merge QR directly with canvas
        function mergeQRDirectly(svgString, callback) {
            console.log("Merging QR directly with canvas");

            var canvasEl = document.getElementById("wcdp-canvas-editor");
            if (!canvasEl) {
                console.log("Canvas element not found for direct merge");
                callback(false);
                return;
            }

            var currentImageData = canvasEl.toDataURL("image/png");
            var canvasImg = new Image();

            canvasImg.onload = function() {
                var svgBlob = new Blob([svgString], {type: 'image/svg+xml;charset=utf-8'});
                var svgURL = URL.createObjectURL(svgBlob);

                var qrImg = new Image();
                qrImg.onload = function() {
                    var tempCanvas = document.createElement('canvas');
                    var tempCtx = tempCanvas.getContext('2d');

                    tempCanvas.width = canvasImg.width;
                    tempCanvas.height = canvasImg.height;

                    // Draw original canvas
                    tempCtx.drawImage(canvasImg, 0, 0);

                    // Calculate QR position (center)
                    var qrSize = Math.min(canvasImg.width, canvasImg.height) * 0.2;
                    var qrX = (canvasImg.width - qrSize) / 2;
                    var qrY = (canvasImg.height - qrSize) / 2;

                    // Draw QR code
                    tempCtx.drawImage(qrImg, qrX, qrY, qrSize, qrSize);

                    // Update original canvas
                    var ctx = canvasEl.getContext('2d');
                    ctx.clearRect(0, 0, canvasEl.width, canvasEl.height);
                    ctx.drawImage(tempCanvas, 0, 0, canvasEl.width, canvasEl.height);

                    // Save to WCDP state management system to persist through redraws
                    if (typeof wcdp_save_canvas_state === 'function') {
                        console.log("Saving canvas state to WCDP system");
                        // Create a fabric image from the merged canvas and add it to the fabric canvas
                        // This ensures it gets saved in WCDP's JSON state
                        var mergedDataURL = tempCanvas.toDataURL('image/png');
                        addMergedImageToFabricCanvas(mergedDataURL);
                    }

                    console.log("Direct canvas merge completed successfully");
                    callback(true);

                    URL.revokeObjectURL(svgURL);
                };

                qrImg.onerror = function() {
                    console.log("Failed to load QR image for direct merge");
                    URL.revokeObjectURL(svgURL);
                    callback(false);
                };

                qrImg.src = svgURL;
            };

            canvasImg.onerror = function() {
                console.log("Failed to load canvas image for direct merge");
                callback(false);
            };

            canvasImg.src = currentImageData;
        }

        // Function to add merged image to Fabric canvas for proper state management
        function addMergedImageToFabricCanvas(mergedDataURL) {
            console.log("Adding merged image to Fabric canvas for state persistence");

            // Try to get or initialize Fabric canvas
            var fabricCanvas = window.wcdp_canvas_editor;
            var canvasEl = document.getElementById("wcdp-canvas-editor");

            if (!fabricCanvas && canvasEl) {
                // Try to initialize Fabric canvas if it doesn't exist
                if (typeof fabric !== 'undefined') {
                    try {
                        fabricCanvas = new fabric.Canvas('wcdp-canvas-editor');
                        window.wcdp_canvas_editor = fabricCanvas;
                        console.log("Initialized new Fabric canvas");
                    } catch (e) {
                        console.log("Could not initialize Fabric canvas:", e.message);
                        return;
                    }
                } else {
                    console.log("Fabric.js not available");
                    return;
                }
            }

            if (fabricCanvas && typeof fabric !== 'undefined') {
                // Clear existing objects and add the merged image as a single object
                fabricCanvas.clear();

                fabric.Image.fromURL(mergedDataURL, function(img) {
                    // Scale image to fit canvas
                    var scaleX = fabricCanvas.width / img.width;
                    var scaleY = fabricCanvas.height / img.height;

                    img.set({
                        left: 0,
                        top: 0,
                        scaleX: scaleX,
                        scaleY: scaleY,
                        selectable: false,
                        evented: false,
                        clas: 'merged-with-qr' // Custom class to identify this object
                    });

                    fabricCanvas.add(img);
                    fabricCanvas.renderAll();

                    // Save to WCDP state management
                    if (typeof wcdp_save_canvas_state === 'function') {
                        wcdp_save_canvas_state();
                        console.log("Canvas state saved to WCDP system");
                    }
                }, {
                    crossOrigin: 'anonymous'
                });
            } else {
                console.log("Fabric canvas not available for state management");
            }
        }

        // Function to generate QR code and merge with canvas
        function generateQRAndMergeWithCanvas(qrText, callback) {
            console.log("Generating QR code for text:", qrText);

            // Make AJAX call to generate QR code
            jQuery.ajax({
                url: window.wcdp_qr_ajax.ajax_url,
                type: "POST",
                data: {
                    action: "wcdp_custom_qr_code",
                    nonce: window.wcdp_qr_ajax.nonce,
                    text: qrText,
                    foreground_color: "#000000",
                    background_color: "#FFFFFF",
                    error_correction: "MEDIUM",
                    logo: "scan-me-square",
                    size: 200
                },
                timeout: 15000,
                success: function(response) {
                    console.log("QR API response:", response);

                    if (response.success && response.svg) {
                        // Convert SVG to image and merge with canvas
                        convertSVGToImageAndMerge(response.svg, callback);
                    } else {
                        console.log("QR API error:", response.error || 'Unknown error');
                        callback(false);
                    }
                },
                error: function(xhr, status, error) {
                    console.error("QR API AJAX error:", status, error);
                    callback(false);
                }
            });
        }

        // Function to convert SVG to image and merge with canvas
        function convertSVGToImageAndMerge(svgString, callback) {
            console.log("Converting SVG to image and merging with canvas");

            // Get current canvas as image
            var canvasEl = document.getElementById("wcdp-canvas-editor");
            if (!canvasEl) {
                console.log("Canvas element not found");
                callback(false);
                return;
            }

            var canvasDataURL = canvasEl.toDataURL("image/png");

            // Create images for both canvas and QR code
            var canvasImg = new Image();
            var qrImg = new Image();

            canvasImg.onload = function() {
                // Convert SVG to data URL
                var svgBlob = new Blob([svgString], {type: 'image/svg+xml;charset=utf-8'});
                var svgURL = URL.createObjectURL(svgBlob);

                qrImg.onload = function() {
                    // Create temporary canvas for merging
                    var tempCanvas = document.createElement('canvas');
                    var tempCtx = tempCanvas.getContext('2d');

                    // Set canvas size to match original
                    tempCanvas.width = canvasImg.width;
                    tempCanvas.height = canvasImg.height;

                    // Draw original canvas image
                    tempCtx.drawImage(canvasImg, 0, 0);

                    // Calculate QR code position (center of canvas)
                    var qrSize = Math.min(canvasImg.width, canvasImg.height) * 0.2; // 20% of canvas size
                    var qrX = (canvasImg.width - qrSize) / 2;
                    var qrY = (canvasImg.height - qrSize) / 2;

                    // Draw QR code on top
                    tempCtx.drawImage(qrImg, qrX, qrY, qrSize, qrSize);

                    // Get merged image data
                    var mergedDataURL = tempCanvas.toDataURL("image/png");

                    // Update the canvas with merged image
                    updateCanvasWithMergedImage(mergedDataURL, callback);

                    // Clean up
                    URL.revokeObjectURL(svgURL);
                };

                qrImg.onerror = function() {
                    console.log("Failed to load QR image");
                    URL.revokeObjectURL(svgURL);
                    callback(false);
                };

                qrImg.src = svgURL;
            };

            canvasImg.onerror = function() {
                console.log("Failed to load canvas image");
                callback(false);
            };

            canvasImg.src = canvasDataURL;
        }

        // Function to update canvas with merged image
        function updateCanvasWithMergedImage(mergedDataURL, callback) {
            console.log("Updating canvas with merged image");

            // Get fabric canvas instance with multiple attempts
            var fabricCanvas = window.wcdp_canvas_editor;
            if (!fabricCanvas) {
                var canvasEl = document.getElementById("wcdp-canvas-editor");
                if (canvasEl && canvasEl.__fabric) {
                    fabricCanvas = canvasEl.__fabric;
                    window.wcdp_canvas_editor = fabricCanvas;
                    console.log("Found Fabric canvas via element.__fabric");
                }
            }

            // If still no fabric canvas, try to wait for it or create one
            if (!fabricCanvas) {
                console.log("Fabric canvas not immediately available, attempting to initialize...");

                // Try to wait for canvas initialization
                var attempts = 0;
                var maxAttempts = 10;

                function waitForCanvas() {
                    attempts++;
                    var canvasEl = document.getElementById("wcdp-canvas-editor");

                    if (canvasEl && canvasEl.__fabric) {
                        fabricCanvas = canvasEl.__fabric;
                        window.wcdp_canvas_editor = fabricCanvas;
                        console.log("Fabric canvas found after", attempts, "attempts");
                        proceedWithUpdate();
                        return;
                    }

                    if (attempts < maxAttempts) {
                        setTimeout(waitForCanvas, 500);
                    } else {
                        console.log("Could not find Fabric canvas after", maxAttempts, "attempts");
                        // Try to update the canvas element directly as fallback
                        updateCanvasElementDirectly(mergedDataURL, callback);
                        return;
                    }
                }

                waitForCanvas();
                return;
            }

            proceedWithUpdate();

            function proceedWithUpdate() {
                if (!fabricCanvas) {
                    console.log("Fabric canvas not available for update");
                    callback(false);
                    return;
                }
            }

                // Create fabric image from merged data
                fabric.Image.fromURL(mergedDataURL, function(img) {
                    // Clear canvas and add merged image as background
                    fabricCanvas.clear();

                    // Scale image to fit canvas
                    var scaleX = fabricCanvas.width / img.width;
                    var scaleY = fabricCanvas.height / img.height;

                    img.set({
                        left: 0,
                        top: 0,
                        scaleX: scaleX,
                        scaleY: scaleY,
                        selectable: false,
                        evented: false
                    });

                    fabricCanvas.add(img);
                    fabricCanvas.renderAll();

                    console.log("Canvas updated with merged image successfully");
                    callback(true);
                }, {
                    crossOrigin: 'anonymous'
                });
            }

        // Fallback function to update canvas element directly
        function updateCanvasElementDirectly(mergedDataURL, callback) {
            console.log("Using fallback: updating canvas element directly");

            var canvasEl = document.getElementById("wcdp-canvas-editor");
            if (!canvasEl) {
                console.log("Canvas element not found for direct update");
                callback(false);
                return;
            }

            var ctx = canvasEl.getContext('2d');
            var img = new Image();

            img.onload = function() {
                // Clear canvas and draw merged image
                ctx.clearRect(0, 0, canvasEl.width, canvasEl.height);
                ctx.drawImage(img, 0, 0, canvasEl.width, canvasEl.height);

                console.log("Canvas element updated directly with merged image");
                callback(true);
            };

            img.onerror = function() {
                console.log("Failed to load merged image for direct canvas update");
                callback(false);
            };

            img.src = mergedDataURL;
        }

        // Test function for complete save integration
        window.testSaveWithQRIntegration = function(testText) {
            testText = testText || "Test Save QR Integration";
            console.log("Testing complete save with QR integration workflow...");

            // Step 1: Set QR text in the field
            var qrTextField = document.getElementById("wcdp-text-box-qr");
            if (qrTextField) {
                qrTextField.value = testText;
                console.log("✓ Set QR text field to:", testText);
            } else {
                console.log("✗ QR text field not found");
                return false;
            }

            // Step 2: Test the save with QR function directly
            if (typeof window.wcdp_save_design_with_qr === 'function') {
                console.log("✓ wcdp_save_design_with_qr function is available");

                // Test the function with a mock save callback
                window.wcdp_save_design_with_qr("Test Design", function() {
                    console.log("✓ Save callback executed successfully");
                    console.log("Complete integration test finished!");
                });

                return true;
            } else {
                console.log("✗ wcdp_save_design_with_qr function not available");
                return false;
            }
        };

        // Direct QR generation test that bypasses Fabric.js
        window.testDirectQRGeneration = function(testText) {
            testText = testText || "Direct QR Test";
            console.log("Testing direct QR generation without Fabric dependency...");

            // Generate QR code directly
            jQuery.ajax({
                url: window.wcdp_qr_ajax.ajax_url,
                type: "POST",
                data: {
                    action: "wcdp_custom_qr_code",
                    nonce: window.wcdp_qr_ajax.nonce,
                    text: testText,
                    foreground_color: "#000000",
                    background_color: "#FFFFFF",
                    error_correction: "MEDIUM",
                    logo: "scan-me-square",
                    size: 200
                },
                timeout: 15000,
                success: function(response) {
                    console.log("✓ QR API response received:", response);

                    if (response.success && response.svg) {
                        console.log("✓ QR SVG generated successfully, length:", response.svg.length);

                        // Store QR data for persistence
                        if (window.wcdp_qr_persistence) {
                            window.wcdp_qr_persistence.store(testText, response.svg);
                        }

                        // Test direct canvas update
                        testDirectCanvasUpdate(response.svg);
                    } else {
                        console.log("✗ QR API error:", response.error || 'Unknown error');
                    }
                },
                error: function(xhr, status, error) {
                    console.error("✗ QR API AJAX error:", status, error);
                }
            });
        };

        // Test direct canvas update without Fabric.js
        function testDirectCanvasUpdate(svgString) {
            console.log("Testing direct canvas update...");

            var canvasEl = document.getElementById("wcdp-canvas-editor");
            if (!canvasEl) {
                console.log("✗ Canvas element not found");
                return;
            }

            console.log("✓ Canvas element found, dimensions:", canvasEl.width + "x" + canvasEl.height);

            // Get current canvas content
            var currentImageData = canvasEl.toDataURL("image/png");
            console.log("✓ Got current canvas data, length:", currentImageData.length);

            // Create images for merging
            var canvasImg = new Image();
            canvasImg.onload = function() {
                console.log("✓ Canvas image loaded");

                // Convert SVG to image
                var svgBlob = new Blob([svgString], {type: 'image/svg+xml;charset=utf-8'});
                var svgURL = URL.createObjectURL(svgBlob);

                var qrImg = new Image();
                qrImg.onload = function() {
                    console.log("✓ QR image loaded");

                    // Create temporary canvas for merging
                    var tempCanvas = document.createElement('canvas');
                    var tempCtx = tempCanvas.getContext('2d');

                    tempCanvas.width = canvasImg.width;
                    tempCanvas.height = canvasImg.height;

                    // Draw original canvas
                    tempCtx.drawImage(canvasImg, 0, 0);

                    // Calculate QR position (center) - use temp canvas dimensions
                    var qrSize = Math.min(tempCanvas.width, tempCanvas.height) * 0.2;
                    var qrX = (tempCanvas.width - qrSize) / 2;
                    var qrY = (tempCanvas.height - qrSize) / 2;

                    console.log("✓ QR positioning - TempCanvas:", tempCanvas.width + "x" + tempCanvas.height,
                               "Canvas:", canvasEl.width + "x" + canvasEl.height,
                               "QR size:", qrSize, "Position:", qrX + "," + qrY);

                    // Draw QR code at calculated position on temp canvas
                    tempCtx.drawImage(qrImg, qrX, qrY, qrSize, qrSize);

                    // Update original canvas - draw temp canvas directly
                    var ctx = canvasEl.getContext('2d');
                    ctx.clearRect(0, 0, canvasEl.width, canvasEl.height);
                    ctx.drawImage(tempCanvas, 0, 0);

                    // Save to WCDP state management system to persist through redraws
                    if (typeof wcdp_save_canvas_state === 'function') {
                        console.log("✓ Saving canvas state to WCDP system for persistence");
                        var mergedDataURL = tempCanvas.toDataURL('image/png');
                        addMergedImageToFabricCanvas(mergedDataURL);
                    }

                    console.log("✓ Canvas updated with QR code successfully!");

                    URL.revokeObjectURL(svgURL);
                };

                qrImg.onerror = function() {
                    console.log("✗ Failed to load QR image");
                    URL.revokeObjectURL(svgURL);
                };

                qrImg.src = svgURL;
            };

            canvasImg.onerror = function() {
                console.log("✗ Failed to load canvas image");
            };

            canvasImg.src = currentImageData;
        }

        // Test function to simulate clicking the save button
        window.testSaveButtonWithQR = function(testText) {
            testText = testText || "Save Button QR Test";
            console.log("Testing save button click with QR integration...");

            // Set QR text
            var qrTextField = document.getElementById("wcdp-text-box-qr");
            if (qrTextField) {
                qrTextField.value = testText;
                console.log("✓ Set QR text field to:", testText);
            }

            // Find and click the save button
            var saveButton = document.getElementById("wcdp-btn-save");
            if (saveButton) {
                console.log("✓ Found save button, triggering click...");
                saveButton.parentElement.click();
                return true;
            } else {
                console.log("✗ Save button not found");
                return false;
            }
        };

        // Enhanced diagnostic function to understand canvas behavior
        window.diagnoseCanvasBehavior = function() {
            console.log("=== Canvas Behavior Diagnosis ===");

            var canvasEl = document.getElementById("wcdp-canvas-editor");
            if (!canvasEl) {
                console.log("✗ Canvas element not found");
                return;
            }

            console.log("✓ Canvas element found");
            console.log("Canvas dimensions:", canvasEl.width + "x" + canvasEl.height);
            console.log("Canvas style dimensions:", canvasEl.style.width + "x" + canvasEl.style.height);

            // Check for Fabric.js
            console.log("Fabric.js available:", typeof fabric !== 'undefined');
            console.log("Canvas has __fabric:", !!canvasEl.__fabric);
            console.log("Global wcdp_canvas_editor:", typeof window.wcdp_canvas_editor);

            // Check WCDP functions
            console.log("wcdp_save_canvas_state function:", typeof wcdp_save_canvas_state);
            console.log("wcdp_canvas_get_json function:", typeof wcdp_canvas_get_json);
            console.log("wcdp_parameters available:", typeof wcdp_parameters !== 'undefined');

            if (typeof wcdp_parameters !== 'undefined' && wcdp_parameters.jsonSides) {
                console.log("WCDP jsonSides available:", Object.keys(wcdp_parameters.jsonSides));
            }

            // Test canvas events
            console.log("Testing canvas resize behavior...");
            var originalWidth = canvasEl.width;

            // Add a resize listener to see what happens
            window.addEventListener('resize', function() {
                console.log("Window resized - Canvas dimensions now:", canvasEl.width + "x" + canvasEl.height);
                if (canvasEl.__fabric) {
                    console.log("Fabric objects after resize:", canvasEl.__fabric.getObjects().length);
                }
            });

            return {
                canvasElement: !!canvasEl,
                fabricAvailable: typeof fabric !== 'undefined',
                canvasHasFabric: !!canvasEl.__fabric,
                globalCanvas: typeof window.wcdp_canvas_editor,
                saveFunction: typeof wcdp_save_canvas_state,
                jsonFunction: typeof wcdp_canvas_get_json,
                wcdpParams: typeof wcdp_parameters !== 'undefined'
            };
        };

        // Diagnostic function to check integration status
        window.diagnoseSaveQRIntegration = function() {
            console.log("=== Save QR Integration Diagnosis ===");

            // Check canvas element details
            var canvasEl = document.getElementById("wcdp-canvas-editor");
            var fabricCanvas = null;

            if (canvasEl) {
                console.log("Canvas element details:", {
                    width: canvasEl.width,
                    height: canvasEl.height,
                    hasFabric: !!canvasEl.__fabric,
                    fabricType: typeof canvasEl.__fabric
                });

                if (canvasEl.__fabric) {
                    fabricCanvas = canvasEl.__fabric;
                    window.wcdp_canvas_editor = fabricCanvas;
                    console.log("Found Fabric canvas via element.__fabric");
                }
            }

            // Check global variables
            if (!fabricCanvas && typeof window.wcdp_canvas_editor !== 'undefined') {
                fabricCanvas = window.wcdp_canvas_editor;
                console.log("Found Fabric canvas via global variable");
            }

            var results = {
                qrTextField: !!document.getElementById("wcdp-text-box-qr"),
                saveButton: !!document.getElementById("wcdp-btn-save"),
                qrAjaxObject: typeof window.wcdp_qr_ajax !== 'undefined',
                saveWithQRFunction: typeof window.wcdp_save_design_with_qr === 'function',
                fabricCanvas: !!fabricCanvas,
                canvasElement: !!document.getElementById("wcdp-canvas-editor")
            };

            console.log("QR text field exists:", results.qrTextField);
            console.log("Save button exists:", results.saveButton);
            console.log("QR AJAX object available:", results.qrAjaxObject);
            console.log("Save with QR function available:", results.saveWithQRFunction);
            console.log("Fabric canvas available:", results.fabricCanvas);
            console.log("Canvas element exists:", results.canvasElement);

            if (fabricCanvas) {
                console.log("Fabric canvas objects count:", fabricCanvas.getObjects().length);
                console.log("Fabric canvas dimensions:", fabricCanvas.width + "x" + fabricCanvas.height);
            }

            var allGood = Object.values(results).every(Boolean);
            console.log("Overall status:", allGood ? "✓ All components ready" : "✗ Some components missing");

            if (!allGood) {
                console.log("Missing components:");
                Object.keys(results).forEach(key => {
                    if (!results[key]) {
                        console.log("  - " + key);
                    }
                });
            }

            return results;
        };

        // Test persistence system
        window.testQRPersistence = function(testText) {
            testText = testText || "Persistence Test QR";
            console.log("Testing QR persistence system...");

            // First generate QR code
            testDirectQRGeneration(testText);

            // Wait a bit then test persistence
            setTimeout(function() {
                console.log("Testing persistence - simulating canvas redraw...");

                if (window.wcdp_qr_persistence && window.wcdp_qr_persistence.isActive) {
                    console.log("✓ QR persistence is active");
                    console.log("Stored QR data:", window.wcdp_qr_persistence.qrData);

                    // Simulate a canvas clear and reapply
                    var canvasEl = document.getElementById("wcdp-canvas-editor");
                    if (canvasEl) {
                        var ctx = canvasEl.getContext('2d');
                        ctx.clearRect(0, 0, canvasEl.width, canvasEl.height);
                        console.log("Canvas cleared, reapplying QR...");

                        window.wcdp_qr_persistence.reapply();
                    }
                } else {
                    console.log("✗ QR persistence not active");
                }
            }, 2000);
        };

        // Clear QR persistence
        window.clearQRPersistence = function() {
            if (window.wcdp_qr_persistence) {
                window.wcdp_qr_persistence.clear();
                console.log("QR persistence cleared");

                // Also clear the canvas to remove any existing QR code
                var canvasEl = document.getElementById("wcdp-canvas-editor");
                if (canvasEl) {
                    var ctx = canvasEl.getContext('2d');
                    ctx.clearRect(0, 0, canvasEl.width, canvasEl.height);
                    console.log("Canvas cleared");
                }
            }
        };

        // Function to temporarily disable QR persistence (useful for canvas operations)
        window.pauseQRPersistence = function() {
            if (window.wcdp_qr_persistence) {
                window.wcdp_qr_persistence.isActive = false;
                console.log("QR persistence paused");
            }
        };

        // Function to resume QR persistence
        window.resumeQRPersistence = function() {
            if (window.wcdp_qr_persistence && window.wcdp_qr_persistence.qrData) {
                window.wcdp_qr_persistence.isActive = true;
                console.log("QR persistence resumed");
                window.wcdp_qr_persistence.reapply();
            }
        };

        // QR persistence system - stores QR data to reapply after canvas redraws
        window.wcdp_qr_persistence = {
            qrData: null,
            isActive: false,

            // Store QR data for persistence
            store: function(qrText, svgString) {
                this.qrData = {
                    text: qrText,
                    svg: svgString,
                    timestamp: Date.now()
                };
                this.isActive = true;
                console.log("QR data stored for persistence:", qrText);
            },

            // Clear stored QR data
            clear: function() {
                this.qrData = null;
                this.isActive = false;
                console.log("QR persistence data cleared");
            },

            // Reapply QR code to canvas
            reapply: function() {
                if (!this.isActive || !this.qrData) {
                    return;
                }

                console.log("Reapplying QR code after canvas redraw");
                var self = this;

                // Small delay to ensure canvas is ready
                setTimeout(function() {
                    self.applyQRToCanvas(self.qrData.svg);
                }, 100);
            },

            // Apply QR to canvas directly
            applyQRToCanvas: function(svgString) {
                var canvasEl = document.getElementById("wcdp-canvas-editor");
                if (!canvasEl) return;

                var currentImageData = canvasEl.toDataURL("image/png");
                var canvasImg = new Image();

                canvasImg.onload = function() {
                    var svgBlob = new Blob([svgString], {type: 'image/svg+xml;charset=utf-8'});
                    var svgURL = URL.createObjectURL(svgBlob);

                    var qrImg = new Image();
                    qrImg.onload = function() {
                        var tempCanvas = document.createElement('canvas');
                        var tempCtx = tempCanvas.getContext('2d');

                        tempCanvas.width = canvasImg.width;
                        tempCanvas.height = canvasImg.height;

                        // Draw original canvas
                        tempCtx.drawImage(canvasImg, 0, 0);

                        // Calculate QR position (center) - use temp canvas dimensions for drawing
                        var qrSize = Math.min(tempCanvas.width, tempCanvas.height) * 0.2;
                        var qrX = (tempCanvas.width - qrSize) / 2;
                        var qrY = (tempCanvas.height - qrSize) / 2;

                        console.log("QR positioning - TempCanvas:", tempCanvas.width + "x" + tempCanvas.height,
                                   "Canvas:", canvasEl.width + "x" + canvasEl.height,
                                   "QR size:", qrSize, "Position:", qrX + "," + qrY);

                        // Draw QR code at calculated position on temp canvas
                        tempCtx.drawImage(qrImg, qrX, qrY, qrSize, qrSize);

                        // Update original canvas - draw temp canvas directly without scaling
                        var ctx = canvasEl.getContext('2d');
                        ctx.clearRect(0, 0, canvasEl.width, canvasEl.height);

                        // Draw the merged temp canvas directly (should be same size as original canvas)
                        ctx.drawImage(tempCanvas, 0, 0);

                        console.log("QR code reapplied successfully");
                        URL.revokeObjectURL(svgURL);
                    };

                    qrImg.src = svgURL;
                };

                canvasImg.src = currentImageData;
            }
        };

        // Hook into window resize and other events to reapply QR
        window.addEventListener('resize', function() {
            if (window.wcdp_qr_persistence.isActive) {
                console.log("Window resize detected, reapplying QR code");
                setTimeout(function() {
                    window.wcdp_qr_persistence.reapply();
                }, 200);
            }
        });

        // Remove the canvas clearing detection to prevent infinite loops
        // Instead, we'll rely on resize and mutation observers only

        // Disable mutation observer to prevent infinite loops
        // Only rely on window resize events for now

        // Simple QR test without persistence (to avoid infinite loops)
        window.testSimpleQR = function(testText) {
            testText = testText || "Simple QR Test";
            console.log("Testing simple QR generation without persistence...");

            // Clear any existing persistence first
            if (window.wcdp_qr_persistence) {
                window.wcdp_qr_persistence.isActive = false;
            }

            // Generate QR code directly
            jQuery.ajax({
                url: window.wcdp_qr_ajax.ajax_url,
                type: "POST",
                data: {
                    action: "wcdp_custom_qr_code",
                    nonce: window.wcdp_qr_ajax.nonce,
                    text: testText,
                    foreground_color: "#000000",
                    background_color: "#FFFFFF",
                    error_correction: "MEDIUM",
                    logo: "scan-me-square",
                    size: 200
                },
                timeout: 15000,
                success: function(response) {
                    console.log("✓ Simple QR API response received");

                    if (response.success && response.svg) {
                        console.log("✓ QR SVG generated, applying to canvas...");
                        applySimpleQR(response.svg);
                    } else {
                        console.log("✗ QR API error:", response.error || 'Unknown error');
                    }
                },
                error: function(xhr, status, error) {
                    console.error("✗ QR API AJAX error:", status, error);
                }
            });
        };

        // Apply QR to canvas without persistence - using Fabric.js properly
        function applySimpleQR(svgString) {
            console.log("Applying QR using Fabric.js approach...");

            // Try to get Fabric canvas first
            var fabricCanvas = window.wcdp_canvas_editor;
            var canvasEl = document.getElementById("wcdp-canvas-editor");

            if (!fabricCanvas && canvasEl && canvasEl.__fabric) {
                fabricCanvas = canvasEl.__fabric;
                window.wcdp_canvas_editor = fabricCanvas;
            }

            if (fabricCanvas && typeof fabric !== 'undefined') {
                console.log("✓ Using Fabric.js approach");

                // Load SVG and add as Fabric object
                fabric.loadSVGFromString(svgString, function(objects, options) {
                    console.log("✓ SVG loaded, objects:", objects.length);

                    var qrGroup = fabric.util.groupSVGElements(objects, options);

                    // Calculate center position
                    var centerX = fabricCanvas.width / 2;
                    var centerY = fabricCanvas.height / 2;

                    // Set QR properties
                    qrGroup.set({
                        left: centerX,
                        top: centerY,
                        originX: 'center',
                        originY: 'center',
                        clas: 'qr-overlay',
                        selectable: true,
                        evented: true
                    });

                    // Scale QR to appropriate size (20% of canvas)
                    var targetSize = Math.min(fabricCanvas.width, fabricCanvas.height) * 0.2;
                    var currentSize = Math.max(qrGroup.width, qrGroup.height);
                    var scale = targetSize / currentSize;
                    qrGroup.scale(scale);

                    console.log("✓ QR positioned at center:", centerX, centerY, "Scale:", scale);

                    // Add to canvas
                    fabricCanvas.add(qrGroup);
                    fabricCanvas.setActiveObject(qrGroup);
                    fabricCanvas.renderAll();

                    console.log("✓ QR added to Fabric canvas successfully!");
                });

            } else {
                console.log("✓ Fabric not available, using direct canvas approach");
                applySimpleQRDirect(svgString);
            }
        }

        // Fallback direct canvas approach
        function applySimpleQRDirect(svgString) {
            var canvasEl = document.getElementById("wcdp-canvas-editor");
            if (!canvasEl) {
                console.log("✗ Canvas element not found");
                return;
            }

            console.log("Canvas info:", {
                width: canvasEl.width,
                height: canvasEl.height,
                clientWidth: canvasEl.clientWidth,
                clientHeight: canvasEl.clientHeight,
                offsetWidth: canvasEl.offsetWidth,
                offsetHeight: canvasEl.offsetHeight
            });

            var currentImageData = canvasEl.toDataURL("image/png");
            var canvasImg = new Image();

            canvasImg.onload = function() {
                console.log("Original image loaded:", canvasImg.width + "x" + canvasImg.height);

                var svgBlob = new Blob([svgString], {type: 'image/svg+xml;charset=utf-8'});
                var svgURL = URL.createObjectURL(svgBlob);

                var qrImg = new Image();
                qrImg.onload = function() {
                    console.log("QR image loaded:", qrImg.width + "x" + qrImg.height);

                    var tempCanvas = document.createElement('canvas');
                    var tempCtx = tempCanvas.getContext('2d');

                    // Use the actual image dimensions, not canvas element dimensions
                    tempCanvas.width = canvasImg.width;
                    tempCanvas.height = canvasImg.height;

                    console.log("Temp canvas size:", tempCanvas.width + "x" + tempCanvas.height);

                    // Draw original image at full size
                    tempCtx.drawImage(canvasImg, 0, 0);

                    // Calculate QR position (center) using actual image dimensions
                    var qrSize = Math.min(tempCanvas.width, tempCanvas.height) * 0.2;
                    var qrX = (tempCanvas.width - qrSize) / 2;
                    var qrY = (tempCanvas.height - qrSize) / 2;

                    console.log("✓ QR positioning - Image size:", tempCanvas.width + "x" + tempCanvas.height,
                               "QR size:", qrSize, "Position:", qrX + "," + qrY);

                    // Draw QR code at center
                    tempCtx.drawImage(qrImg, qrX, qrY, qrSize, qrSize);

                    // Update original canvas - scale to fit canvas element
                    var ctx = canvasEl.getContext('2d');
                    ctx.clearRect(0, 0, canvasEl.width, canvasEl.height);
                    ctx.drawImage(tempCanvas, 0, 0, tempCanvas.width, tempCanvas.height,
                                 0, 0, canvasEl.width, canvasEl.height);

                    console.log("✓ Direct QR applied successfully!");
                    URL.revokeObjectURL(svgURL);
                };

                qrImg.onerror = function() {
                    console.log("✗ Failed to load QR image");
                    URL.revokeObjectURL(svgURL);
                };

                qrImg.src = svgURL;
            };

            canvasImg.onerror = function() {
                console.log("✗ Failed to load canvas image");
            };

            canvasImg.src = currentImageData;
        }

        // Function to add QR as proper Fabric object that persists
        window.addQRToFabricCanvas = function(qrText) {
            qrText = qrText || "Fabric QR Test";
            console.log("Adding QR as Fabric object for persistence...");

            var fabricCanvas = window.wcdp_canvas_editor;
            if (!fabricCanvas) {
                var canvasEl = document.getElementById("wcdp-canvas-editor");
                if (canvasEl && canvasEl.__fabric) {
                    fabricCanvas = canvasEl.__fabric;
                    window.wcdp_canvas_editor = fabricCanvas;
                }
            }

            if (!fabricCanvas) {
                console.log("✗ Fabric canvas not available");
                return;
            }

            // Generate QR code
            jQuery.ajax({
                url: window.wcdp_qr_ajax.ajax_url,
                type: "POST",
                data: {
                    action: "wcdp_custom_qr_code",
                    nonce: window.wcdp_qr_ajax.nonce,
                    text: qrText,
                    foreground_color: "#000000",
                    background_color: "#FFFFFF",
                    error_correction: "MEDIUM",
                    logo: "scan-me-square",
                    size: 200
                },
                timeout: 15000,
                success: function(response) {
                    if (response.success && response.svg) {
                        console.log("✓ QR generated, adding to Fabric canvas...");

                        fabric.loadSVGFromString(response.svg, function(objects, options) {
                            var qrGroup = fabric.util.groupSVGElements(objects, options);

                            // Position at center
                            qrGroup.set({
                                left: fabricCanvas.width / 2,
                                top: fabricCanvas.height / 2,
                                originX: 'center',
                                originY: 'center',
                                clas: 'qr',
                                selectable: true,
                                evented: true,
                                id: 'qr-' + Date.now()
                            });

                            // Scale appropriately
                            var targetSize = Math.min(fabricCanvas.width, fabricCanvas.height) * 0.2;
                            var currentSize = Math.max(qrGroup.width, qrGroup.height);
                            qrGroup.scale(targetSize / currentSize);

                            // Add to canvas
                            fabricCanvas.add(qrGroup);
                            fabricCanvas.setActiveObject(qrGroup);
                            fabricCanvas.renderAll();

                            // Save state to WCDP system
                            if (typeof wcdp_save_canvas_state === 'function') {
                                wcdp_save_canvas_state();
                                console.log("✓ Canvas state saved to WCDP");
                            }

                            console.log("✓ QR added as Fabric object successfully!");
                        });
                    }
                },
                error: function(xhr, status, error) {
                    console.error("✗ QR generation failed:", error);
                }
            });
        };

        console.log("WCDP QR: All functions loaded. Try: testQRWithForceInit('Hello World')");
        console.log("WCDP QR: Save integration functions loaded. Try: testSaveWithQRIntegration('Test Text')");
        console.log("WCDP QR: Simple QR test available. Try: testSimpleQR('Test Text')");
        console.log("WCDP QR: Fabric QR integration available. Try: addQRToFabricCanvas('Test Text')");
        </script>
        <?php
    }
}
add_action('wp_footer', 'wcdp_qr_direct_script', 999);
