import React, { useRef } from "react";

const TShirtQRComposer: React.FC = () => {
  const canvasRef = useRef<HTMLCanvasElement | null>(null);

  const handleMergeImages = async () => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    const tshirtImg = new Image();
    const qrImg = new Image();

    tshirtImg.crossOrigin = "anonymous"; // for CORS if image is hosted externally
    qrImg.crossOrigin = "anonymous";

    // Replace with actual image URLs or local paths
    tshirtImg.src = "/images/tshirt.png";
    qrImg.src = "/images/qrcode.png";

    tshirtImg.onload = () => {
      canvas.width = tshirtImg.width;
      canvas.height = tshirtImg.height;
      ctx.drawImage(tshirtImg, 0, 0);

      qrImg.onload = () => {
        // Adjust position and size of QR code
        const qrSize = 150;
        const x = 100;
        const y = 300;
        ctx.drawImage(qrImg, x, y, qrSize, qrSize);

        // Optional: export to image
        const mergedImageURL = canvas.toDataURL("image/png");
        const link = document.createElement("a");
        link.href = mergedImageURL;
        link.download = "tshirt_with_qr.png";
        link.click();
      };
    };
  };

  return (
    <div>
      <canvas ref={canvasRef} style={{ display: "none" }} />
      <button onClick={handleMergeImages}>Download T-shirt with QR</button>
    </div>
  );
};

export default TShirtQRComposer;