# WCDP QR Code Save Integration

This document explains the QR code save integration functionality that has been implemented for WooCommerce Designer Pro (WCDP).

## Overview

The integration adds automatic QR code generation and merging functionality to the WCDP save design process. When a user saves a design and has entered text in the QR code field, the system will:

1. Generate a QR code using the custom QR API
2. Merge the QR code with the current canvas design
3. Update the canvas with the merged image
4. Proceed with the normal save process

## Features Implemented

### 1. Save Modal Integration
- **Location**: Modified `assets/js/wcdp-content-editor.js` lines 808-821
- **Functionality**: The save button in the "Enter a title for the design (Optional):" modal now checks for QR text and generates/merges QR codes before saving

### 2. QR Code Generation and Merging
- **Location**: Added to `qr-code-generator.php` lines 1484-1671
- **Functions**:
  - `wcdp_save_design_with_qr()`: Main integration function
  - `generateQRAndMergeWithCanvas()`: Handles QR generation and merging
  - `convertSVGToImageAndMerge()`: Converts SVG QR code to image and merges with canvas
  - `updateCanvasWithMergedImage()`: Updates the Fabric.js canvas with merged result

### 3. Image Merging Logic
- **Canvas Capture**: Gets current canvas content as PNG data URL
- **QR Positioning**: Places QR code in the center of the canvas
- **QR Sizing**: QR code size is 20% of the canvas dimensions
- **Layer Order**: QR code is placed on top of the existing design

### 4. Testing Functions
- **Location**: Added to `qr-code-generator.php` lines 1667-1759
- **Functions**:
  - `testSaveWithQRIntegration()`: Tests the complete save workflow
  - `testSaveButtonWithQR()`: Simulates save button click with QR
  - `diagnoseSaveQRIntegration()`: Diagnostic function to check integration status

## Usage Instructions

### For End Users

1. **Open WCDP Designer**: Navigate to the WCDP designer page
2. **Create Design**: Add your design elements to the canvas
3. **Add QR Text**: 
   - Click on the QR code tab in the tool panel
   - Enter text in the "wcdp-text-box-qr" field
4. **Save Design**:
   - Click the Save button
   - Enter a design title in the modal
   - Click "Save Design"
   - The system will automatically generate a QR code and merge it with your design

### For Developers/Testing

1. **Test Integration**: Open browser console and run:
   ```javascript
   diagnoseSaveQRIntegration()
   ```

2. **Test QR Generation**: 
   ```javascript
   testSaveWithQRIntegration('Your QR Text Here')
   ```

3. **Test Save Button**:
   ```javascript
   testSaveButtonWithQR('Test QR Code')
   ```

4. **Use Test Page**: Open `test-qr-save-integration.html` in the WCDP environment

## Technical Details

### Dependencies
- **WCDP Core**: Requires WCDP plugin to be active
- **QR Code Generator Plugin**: Custom QR code generator must be loaded
- **Fabric.js**: Uses Fabric.js canvas for image manipulation
- **jQuery**: Uses jQuery for DOM manipulation and AJAX calls

### API Integration
- **QR API Endpoint**: `wp_ajax_wcdp_custom_qr_code`
- **Request Format**: POST with nonce, text, colors, error correction, logo, size
- **Response Format**: JSON with success flag and SVG content

### Canvas Integration
- **Canvas Element**: `#wcdp-canvas-editor`
- **Fabric Instance**: `window.wcdp_canvas_editor`
- **Image Format**: PNG data URLs for merging
- **Scaling**: Automatic scaling to fit canvas dimensions

## Error Handling

The integration includes comprehensive error handling:

1. **Missing QR Text**: If no QR text is provided, normal save proceeds
2. **API Failures**: If QR generation fails, normal save proceeds with warning
3. **Canvas Issues**: If canvas manipulation fails, normal save proceeds
4. **Missing Dependencies**: Graceful fallback to normal save functionality

## Troubleshooting

### Common Issues

1. **QR Code Not Generated**:
   - Check that QR text field has content
   - Verify QR code generator plugin is active
   - Check browser console for API errors

2. **Canvas Not Updated**:
   - Ensure Fabric.js canvas is properly initialized
   - Check that `wcdp_canvas_editor` global variable exists
   - Verify canvas element exists in DOM

3. **Save Integration Not Working**:
   - Run `diagnoseSaveQRIntegration()` to check component status
   - Verify all required functions are loaded
   - Check for JavaScript errors in console

### Debug Functions

```javascript
// Check integration status
diagnoseSaveQRIntegration()

// Test QR generation only
testQRWithForceInit('Test Text')

// Test complete save workflow
testSaveWithQRIntegration('Test Text')

// Test save button simulation
testSaveButtonWithQR('Test Text')
```

## Files Modified

1. **qr-code-generator.php**: Added save integration functions
2. **assets/js/wcdp-content-editor.js**: Modified save button handler
3. **test-qr-save-integration.html**: Created test page (new file)
4. **QR_SAVE_INTEGRATION_README.md**: This documentation (new file)

## Future Enhancements

Potential improvements for future versions:

1. **QR Position Options**: Allow users to choose QR code position
2. **QR Size Options**: Configurable QR code size
3. **Multiple QR Codes**: Support for multiple QR codes per design
4. **QR Styling**: Additional QR code styling options
5. **Batch Processing**: QR generation for multiple designs

## Support

For issues or questions regarding this integration:

1. Check the troubleshooting section above
2. Use the debug functions to identify issues
3. Review browser console for error messages
4. Test with the provided test page
